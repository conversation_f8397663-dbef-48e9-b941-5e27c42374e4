'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Bot,
  Brain,
  Search,
  Play,
  Monitor,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  AlertTriangle,
  Clock,
  Activity,
  Cpu,
  MemoryStick,
  DollarSign,
  Users,
  Zap,
  Target,
  Eye,
  Settings,
  RefreshCw
} from 'lucide-react';

interface AutonomousAgentStatus {
  agentId: string;
  agentType: 'planning' | 'research' | 'execution' | 'resume' | 'monitoring';
  name: string;
  status: 'active' | 'idle' | 'learning' | 'error';
  autonomyScore: number;
  learningProgress: number;
  performanceMetrics: {
    successRate: number;
    responseTime: number;
    decisionsPerHour: number;
    adaptationRate: number;
  };
  resourceUsage: {
    cpu: number;
    memory: number;
    gpu?: number;
  };
  lastActivity: string;
  currentTask?: string;
}

interface SystemMetrics {
  overallAutonomyScore: number;
  totalDecisions: number;
  humanInterventions: number;
  costSavings: number;
  resourceEfficiency: number;
  activeSessions: number;
  mlModelAccuracy: number;
  emergentBehaviors: number;
}

interface UserPodStatus {
  podId: string;
  userId: string;
  status: 'active' | 'idle' | 'terminated';
  resourceUsage: {
    cpu: number;
    memory: number;
  };
  sessionDuration: number;
  currentActivity: string;
  applicationProgress: {
    total: number;
    completed: number;
    inProgress: number;
    failed: number;
  };
}

export default function AutonomousAgentDashboard() {
  const [agents, setAgents] = useState<AutonomousAgentStatus[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [userPods, setUserPods] = useState<UserPodStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [refreshInterval, setRefreshInterval] = useState(5000);

  useEffect(() => {
    loadDashboardData();
    const interval = setInterval(loadDashboardData, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval]);

  const loadDashboardData = async () => {
    try {
      const [agentsResponse, metricsResponse, podsResponse] = await Promise.all([
        fetch('/api/autonomous-agents/status'),
        fetch('/api/autonomous-agents/metrics'),
        fetch('/api/user-pods/status')
      ]);

      const agentsData = await agentsResponse.json();
      const metricsData = await metricsResponse.json();
      const podsData = await podsResponse.json();

      setAgents(agentsData.agents || []);
      setSystemMetrics(metricsData.metrics || null);
      setUserPods(podsData.pods || []);
      setLoading(false);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      setLoading(false);
    }
  };

  const getAgentIcon = (agentType: string) => {
    switch (agentType) {
      case 'planning': return <Brain className="h-5 w-5" />;
      case 'research': return <Search className="h-5 w-5" />;
      case 'execution': return <Play className="h-5 w-5" />;
      case 'resume': return <Bot className="h-5 w-5" />;
      case 'monitoring': return <Monitor className="h-5 w-5" />;
      default: return <Bot className="h-5 w-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'learning': return 'bg-blue-500';
      case 'idle': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getAutonomyScoreColor = (score: number) => {
    if (score >= 95) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">100% Autonomous AI Agents</h1>
          <p className="text-muted-foreground">
            Real-time monitoring of centralized autonomous agent system
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={loadDashboardData} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Badge variant="outline" className="bg-green-50 text-green-700">
            100% Autonomous Mode
          </Badge>
        </div>
      </div>

      {/* System Overview */}
      {systemMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">System Autonomy</p>
                  <p className={`text-2xl font-bold ${getAutonomyScoreColor(systemMetrics.overallAutonomyScore)}`}>
                    {systemMetrics.overallAutonomyScore.toFixed(1)}%
                  </p>
                </div>
                <Target className="h-8 w-8 text-green-600" />
              </div>
              <Progress value={systemMetrics.overallAutonomyScore} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Cost Savings</p>
                  <p className="text-2xl font-bold text-green-600">
                    {systemMetrics.costSavings.toFixed(0)}%
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
              <p className="text-xs text-muted-foreground mt-1">vs per-POD architecture</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Sessions</p>
                  <p className="text-2xl font-bold">{systemMetrics.activeSessions}</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {systemMetrics.humanInterventions} human interventions today
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">ML Accuracy</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {systemMetrics.mlModelAccuracy.toFixed(1)}%
                  </p>
                </div>
                <Zap className="h-8 w-8 text-purple-600" />
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {systemMetrics.emergentBehaviors} emergent behaviors detected
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="agents" className="space-y-4">
        <TabsList>
          <TabsTrigger value="agents">Autonomous Agents</TabsTrigger>
          <TabsTrigger value="pods">User PODs</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="learning">ML Learning</TabsTrigger>
        </TabsList>

        <TabsContent value="agents" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {agents.map((agent) => (
              <Card key={agent.agentId} className="cursor-pointer hover:shadow-lg transition-shadow"
                    onClick={() => setSelectedAgent(agent.agentId)}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getAgentIcon(agent.agentType)}
                      <CardTitle className="text-lg">{agent.name}</CardTitle>
                    </div>
                    <Badge className={`${getStatusColor(agent.status)} text-white`}>
                      {agent.status}
                    </Badge>
                  </div>
                  <CardDescription>
                    Autonomy Score: <span className={getAutonomyScoreColor(agent.autonomyScore)}>
                      {agent.autonomyScore.toFixed(1)}%
                    </span>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Learning Progress</span>
                        <span>{agent.learningProgress.toFixed(1)}%</span>
                      </div>
                      <Progress value={agent.learningProgress} />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-muted-foreground">Success Rate:</span>
                        <span className="ml-1 font-medium">
                          {agent.performanceMetrics.successRate.toFixed(1)}%
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Response:</span>
                        <span className="ml-1 font-medium">
                          {agent.performanceMetrics.responseTime}ms
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Cpu className="h-3 w-3" />
                        {agent.resourceUsage.cpu.toFixed(1)}%
                      </div>
                      <div className="flex items-center gap-1">
                        <MemoryStick className="h-3 w-3" />
                        {agent.resourceUsage.memory.toFixed(1)}%
                      </div>
                      {agent.resourceUsage.gpu && (
                        <div className="flex items-center gap-1">
                          <Zap className="h-3 w-3" />
                          {agent.resourceUsage.gpu.toFixed(1)}%
                        </div>
                      )}
                    </div>

                    {agent.currentTask && (
                      <div className="text-xs">
                        <span className="text-muted-foreground">Current Task:</span>
                        <p className="font-medium truncate">{agent.currentTask}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="pods" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {userPods.map((pod) => (
              <Card key={pod.podId}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">POD {pod.podId.slice(-8)}</CardTitle>
                    <Badge variant={pod.status === 'active' ? 'default' : 'secondary'}>
                      {pod.status}
                    </Badge>
                  </div>
                  <CardDescription>
                    User: {pod.userId} • Session: {Math.floor(pod.sessionDuration / 60)}m
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm font-medium mb-2">Application Progress</p>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="flex items-center gap-1">
                          <CheckCircle className="h-3 w-3 text-green-600" />
                          <span>{pod.applicationProgress.completed} completed</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3 text-blue-600" />
                          <span>{pod.applicationProgress.inProgress} in progress</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Cpu className="h-3 w-3" />
                        {pod.resourceUsage.cpu.toFixed(1)}%
                      </div>
                      <div className="flex items-center gap-1">
                        <MemoryStick className="h-3 w-3" />
                        {pod.resourceUsage.memory.toFixed(1)}%
                      </div>
                    </div>

                    <div className="text-xs">
                      <span className="text-muted-foreground">Current Activity:</span>
                      <p className="font-medium truncate">{pod.currentActivity}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>System Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                {systemMetrics && (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Total Autonomous Decisions</span>
                      <span className="font-bold">{systemMetrics.totalDecisions.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Resource Efficiency</span>
                      <span className="font-bold text-green-600">
                        {systemMetrics.resourceEfficiency.toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Human Intervention Rate</span>
                      <span className="font-bold text-blue-600">
                        {((systemMetrics.humanInterventions / systemMetrics.totalDecisions) * 100).toFixed(2)}%
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cost Efficiency Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                {systemMetrics && (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Infrastructure Cost Reduction</span>
                      <span className="font-bold text-green-600">86%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Performance Improvement</span>
                      <span className="font-bold text-blue-600">300%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Users per VM Instance</span>
                      <span className="font-bold">8-12</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="learning" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Machine Learning Infrastructure Status</CardTitle>
              <CardDescription>
                Real-time status of reinforcement learning, predictive analytics, and pattern recognition
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <Brain className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                  <h3 className="font-semibold">Reinforcement Learning</h3>
                  <p className="text-sm text-muted-foreground">Training Active</p>
                  <Progress value={92} className="mt-2" />
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <TrendingUp className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                  <h3 className="font-semibold">Predictive Analytics</h3>
                  <p className="text-sm text-muted-foreground">96% Accuracy</p>
                  <Progress value={96} className="mt-2" />
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <Eye className="h-8 w-8 mx-auto mb-2 text-green-600" />
                  <h3 className="font-semibold">Pattern Recognition</h3>
                  <p className="text-sm text-muted-foreground">Learning Active</p>
                  <Progress value={88} className="mt-2" />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
